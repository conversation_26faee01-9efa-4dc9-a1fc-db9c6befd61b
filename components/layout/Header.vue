<template>
  <header :class="headerClasses">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <NuxtLink to="/" class="flex items-center space-x-2">
            <Icon name="musical-note" class="w-8 h-8 text-primary-500" />
            <span class="text-xl font-bold text-gray-900 dark:text-white">
              MusicDou
            </span>
          </NuxtLink>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <NuxtLink
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            :class="navLinkClasses"
          >
            {{ item.name }}
          </NuxtLink>
        </nav>

        <!-- Right Side Actions -->
        <div class="flex items-center space-x-4">
          <!-- Search -->
          <button
            v-if="showSearch"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            @click="toggleSearch"
          >
            <Icon name="magnifying-glass" class="w-5 h-5" />
          </button>

          <!-- Theme Toggle -->
          <button
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            @click="toggleTheme"
          >
            <Icon :name="themeIcon" class="w-5 h-5" />
          </button>

          <!-- User Menu -->
          <div v-if="user" class="relative">
            <button
              class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              @click="toggleUserMenu"
            >
              <img
                :src="user.avatar || '/default-avatar.png'"
                :alt="user.name"
                class="w-8 h-8 rounded-full"
              />
              <Icon name="chevron-down" class="w-4 h-4" />
            </button>

            <!-- User Dropdown -->
            <Transition
              enter-active-class="transition ease-out duration-100"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-75"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
              >
                <NuxtLink
                  v-for="item in userMenuItems"
                  :key="item.name"
                  :to="item.href"
                  class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  @click="showUserMenu = false"
                >
                  <Icon :name="item.icon" class="w-4 h-4 inline mr-2" />
                  {{ item.name }}
                </NuxtLink>
                <hr class="my-1 border-gray-200 dark:border-gray-700" />
                <button
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  @click="handleLogout"
                >
                  <Icon name="arrow-right-on-rectangle" class="w-4 h-4 inline mr-2" />
                  退出登录
                </button>
              </div>
            </Transition>
          </div>

          <!-- Login Button -->
          <div v-else class="flex items-center space-x-2">
            <Button variant="ghost" size="sm" @click="navigateTo('/login')">
              登录
            </Button>
            <Button size="sm" @click="navigateTo('/register')">
              注册
            </Button>
          </div>

          <!-- Mobile Menu Button -->
          <button
            class="md:hidden p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            @click="toggleMobileMenu"
          >
            <Icon :name="mobileMenuOpen ? 'x-mark' : 'bars-3'" class="w-6 h-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 -translate-y-1"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-1"
      >
        <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 dark:border-gray-700 py-4">
          <nav class="flex flex-col space-y-2">
            <NuxtLink
              v-for="item in navigation"
              :key="item.name"
              :to="item.href"
              class="px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
              @click="mobileMenuOpen = false"
            >
              {{ item.name }}
            </NuxtLink>
          </nav>
        </div>
      </Transition>
    </div>

    <!-- Search Overlay -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="searchOpen"
        class="absolute inset-x-0 top-full bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-lg z-40"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Input
            v-model="searchQuery"
            type="search"
            placeholder="搜索音乐、歌单、用户..."
            left-icon="MagnifyingGlassIcon"
            clearable
            @keydown.enter="handleSearch"
          />
        </div>
      </div>
    </Transition>
  </header>
</template>

<script setup lang="ts">
interface Props {
  transparent?: boolean
  sticky?: boolean
  showSearch?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  transparent: false,
  sticky: true,
  showSearch: true
})

// 状态
const mobileMenuOpen = ref(false)
const showUserMenu = ref(false)
const searchOpen = ref(false)
const searchQuery = ref('')

// 主题
const { toggleTheme, isDark } = useTheme()
const themeIcon = computed(() => isDark.value ? 'sun' : 'moon')

// 用户状态
const { user, logout } = useAuth()

// 导航菜单
const navigation = [
  { name: '首页', href: '/' },
  { name: '发现', href: '/discover' },
  { name: '歌单', href: '/playlists' },
  { name: '社区', href: '/social' },
  { name: '组件演示', href: '/components-demo' }
]

// 用户菜单
const userMenuItems = [
  { name: '个人资料', href: '/profile', icon: 'user' },
  { name: '我的歌单', href: '/my-playlists', icon: 'queue-list' },
  { name: '收藏', href: '/favorites', icon: 'heart' },
  { name: '设置', href: '/settings', icon: 'cog-6-tooth' }
]

// 方法
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const toggleSearch = () => {
  searchOpen.value = !searchOpen.value
  if (searchOpen.value) {
    nextTick(() => {
      // 聚焦搜索框
    })
  }
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/search?q=${encodeURIComponent(searchQuery.value)}`)
    searchOpen.value = false
  }
}

const handleLogout = async () => {
  await logout()
  showUserMenu.value = false
  navigateTo('/')
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 样式计算
const headerClasses = computed(() => {
  const baseClasses = [
    'relative',
    'z-40',
    'transition-all',
    'duration-200'
  ]

  if (props.transparent) {
    baseClasses.push('bg-transparent')
  } else {
    baseClasses.push(
      'bg-white/80',
      'dark:bg-gray-900/80',
      'backdrop-blur-md',
      'border-b',
      'border-gray-200',
      'dark:border-gray-700'
    )
  }

  if (props.sticky) {
    baseClasses.push('sticky', 'top-0')
  }

  return baseClasses.join(' ')
})

const navLinkClasses = computed(() => {
  return [
    'text-sm',
    'font-medium',
    'text-gray-700',
    'dark:text-gray-300',
    'hover:text-primary-600',
    'dark:hover:text-primary-400',
    'transition-colors',
    'router-link-active:text-primary-600',
    'dark:router-link-active:text-primary-400'
  ].join(' ')
})
</script>
