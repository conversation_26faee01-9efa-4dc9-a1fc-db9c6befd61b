<template>
  <Card 
    :variant="variant"
    :clickable="true"
    class="group overflow-hidden"
    @click="$emit('click', music)"
  >
    <!-- 封面图片 -->
    <div class="relative aspect-square overflow-hidden rounded-t-lg">
      <img 
        v-if="music.coverUrl" 
        :src="music.coverUrl" 
        :alt="music.title"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
      >
      <div 
        v-else 
        class="w-full h-full bg-gray-200 dark:bg-slate-700 flex items-center justify-center"
      >
        <Icon name="musical-note" class="w-12 h-12 text-gray-400" />
      </div>
      
      <!-- 播放按钮覆盖层 -->
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
        <Button
          variant="primary"
          size="sm"
          class="opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-300"
          @click.stop="playMusic"
        >
          <Icon name="play" class="w-5 h-5" />
        </Button>
      </div>
      
      <!-- 时长标签 -->
      <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
        {{ formatDuration(music.duration) }}
      </div>
      
      <!-- 喜欢按钮 -->
      <Button
        v-if="showLikeButton"
        variant="ghost"
        size="sm"
        :class="[
          'absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300',
          music.isLiked ? 'text-red-500' : 'text-white hover:text-red-500'
        ]"
        @click.stop="toggleLike"
      >
        <Icon :name="music.isLiked ? 'heart-solid' : 'heart'" class="w-4 h-4" />
      </Button>
    </div>
    
    <!-- 音乐信息 -->
    <div class="p-4">
      <h3 class="font-medium text-gray-900 dark:text-white truncate mb-1">
        {{ music.title }}
      </h3>
      
      <p class="text-sm text-gray-500 dark:text-gray-400 truncate mb-2">
        {{ music.artist }}
      </p>
      
      <div v-if="showStats" class="flex items-center justify-between text-xs text-gray-400">
        <div class="flex items-center space-x-3">
          <span class="flex items-center space-x-1">
            <Icon name="play" class="w-3 h-3" />
            <span>{{ formatNumber(music.playCount) }}</span>
          </span>
          
          <span class="flex items-center space-x-1">
            <Icon name="heart" class="w-3 h-3" />
            <span>{{ formatNumber(music.likeCount) }}</span>
          </span>
        </div>
        
        <span>{{ formatDate(music.createdAt) }}</span>
      </div>
      
      <!-- 标签 -->
      <div v-if="music.tags && music.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
        <span
          v-for="tag in music.tags.slice(0, 3)"
          :key="tag"
          class="inline-block bg-gray-100 dark:bg-slate-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-1 rounded-full"
        >
          {{ tag }}
        </span>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div v-if="showActions" class="px-4 pb-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            @click.stop="playMusic"
            class="flex items-center space-x-1"
          >
            <Icon name="play" class="w-4 h-4" />
            <span>播放</span>
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            @click.stop="addToQueue"
            class="flex items-center space-x-1"
          >
            <Icon name="plus" class="w-4 h-4" />
            <span>队列</span>
          </Button>
        </div>
        
        <div class="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            @click.stop="addToPlaylist"
          >
            <Icon name="plus-circle" class="w-4 h-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            @click.stop="shareMusic"
          >
            <Icon name="share" class="w-4 h-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            @click.stop="showMoreOptions"
          >
            <Icon name="ellipsis-horizontal" class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

interface Props {
  music: Music
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost'
  showLikeButton?: boolean
  showStats?: boolean
  showActions?: boolean
}

interface Emits {
  (e: 'click', music: Music): void
  (e: 'play', music: Music): void
  (e: 'like', music: Music): void
  (e: 'addToQueue', music: Music): void
  (e: 'addToPlaylist', music: Music): void
  (e: 'share', music: Music): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  showLikeButton: true,
  showStats: true,
  showActions: false
})

const emit = defineEmits<Emits>()

const playerStore = usePlayerStore()
const audioPlayer = useAudioPlayer()

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化数字
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}月前`
  return `${Math.floor(diffDays / 365)}年前`
}

// 播放音乐
const playMusic = () => {
  audioPlayer.playTrack(props.music)
  emit('play', props.music)
}

// 切换喜欢状态
const toggleLike = () => {
  emit('like', props.music)
}

// 添加到播放队列
const addToQueue = () => {
  playerStore.addToQueue(props.music)
  emit('addToQueue', props.music)
}

// 添加到歌单
const addToPlaylist = () => {
  emit('addToPlaylist', props.music)
}

// 分享音乐
const shareMusic = () => {
  emit('share', props.music)
}

// 显示更多选项
const showMoreOptions = () => {
  // 这里可以显示一个下拉菜单或模态框
  console.log('显示更多选项', props.music)
}
</script>
