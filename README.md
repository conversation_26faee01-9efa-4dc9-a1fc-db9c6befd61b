# 🎵 MusicDou Frontend

现代化音乐分享平台前端应用，基于 Nuxt.js 3 + TypeScript 构建。

## 📊 项目状态

**当前版本**: Phase 4 完成
**开发进度**: 62.5% (4/8 阶段完成)
**最后更新**: 2025-08-01 09:30

### ✅ 已完成功能
- ✅ **项目基础设施** - Nuxt.js 3, TypeScript, Tailwind CSS
- ✅ **完整UI组件库** - Button, Input, Card, Modal, Loading, Toast
- ✅ **主题系统** - 深色/浅色主题切换
- ✅ **认证系统** - JWT集成, 路由守卫, 用户管理
- ✅ **音乐播放模块** - 全局播放器, Howler.js集成, 播放队列管理
- ✅ **响应式设计** - 完美适配各种设备

### 🔄 下一阶段
- 🎯 **Phase 5**: Playlist Management - 歌单管理功能开发

## 🚀 快速开始

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## 🎨 技术栈

### 核心框架
- **Nuxt.js 4.0.2** - Vue 3 全栈框架
- **TypeScript** - 类型安全开发
- **Tailwind CSS** - 现代化样式框架
- **Pinia** - Vue 状态管理

### UI/UX
- **@heroicons/vue** - 精美图标库
- **@headlessui/vue** - 无头UI组件
- **@nuxtjs/color-mode** - 主题切换支持

### 认证 & 安全
- **JWT** - 安全的用户认证
- **Cookie存储** - 持久化会话管理
- **路由守卫** - 页面访问控制

### 音频处理
- **Howler.js** - 专业音频播放库

## 📱 功能特性

### 🔐 用户认证
- 用户注册/登录
- JWT token管理
- 密码重置功能
- 个人资料管理
- 安全的会话保持

### 🎨 界面设计
- 响应式设计
- 深色/浅色主题
- 现代化UI组件
- 无障碍访问支持

### 🛡️ 安全特性
- 路由保护中间件
- XSS防护
- 安全的密码处理
- 自动token刷新

## 📂 项目结构

```
musicdou-frontend/
├── components/          # Vue组件
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   └── ...
├── pages/              # 页面路由
├── composables/        # 组合式函数
├── middleware/         # 路由中间件
├── stores/             # Pinia状态管理
├── types/              # TypeScript类型
├── api/                # API文档
└── ...
```

## 🔗 相关文档

- [开发进度跟踪](./DEVELOPMENT_PROGRESS.md)
- [快速启动指南](./QUICK_START_GUIDE.md)
- [项目状态报告](./PROJECT_STATUS.md)
- [开发任务文档](./FRONTEND_DEVELOPMENT_TASKS.md)

## 📄 许可证

MIT License
