{"name": "musicdou-frontend", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --ext .vue,.js,.ts", "lint:fix": "eslint . --ext .vue,.js,.ts --fix", "format": "prettier --write .", "type-check": "nuxt typecheck"}, "dependencies": {"@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.2", "@types/howler": "^2.2.12", "@vueuse/motion": "^3.0.3", "@vueuse/nuxt": "^13.6.0", "howler.js": "^2.1.2", "nuxt": "^4.0.1", "vue": "^3.5.18", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2"}}