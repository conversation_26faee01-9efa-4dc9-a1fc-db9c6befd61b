<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <Loading type="spinner" size="lg" text="加载中..." />
    </div>

    <!-- 音乐详情 -->
    <div v-else-if="music" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 返回按钮 -->
      <Button
        variant="ghost"
        @click="$router.back()"
        class="mb-6 flex items-center space-x-2"
      >
        <Icon name="arrow-left" class="w-4 h-4" />
        <span>返回</span>
      </Button>

      <!-- 音乐信息区域 -->
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden mb-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 p-8">
          <!-- 封面图片 -->
          <div class="lg:col-span-1">
            <div class="aspect-square rounded-lg overflow-hidden shadow-lg">
              <img 
                v-if="music.coverUrl" 
                :src="music.coverUrl" 
                :alt="music.title"
                class="w-full h-full object-cover"
              >
              <div 
                v-else 
                class="w-full h-full bg-gray-200 dark:bg-slate-700 flex items-center justify-center"
              >
                <Icon name="musical-note" class="w-24 h-24 text-gray-400" />
              </div>
            </div>
          </div>

          <!-- 音乐信息 -->
          <div class="lg:col-span-2 space-y-6">
            <!-- 基本信息 -->
            <div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {{ music.title }}
              </h1>
              <p class="text-xl text-gray-600 dark:text-gray-300 mb-4">
                {{ music.artist }}
              </p>
              <p v-if="music.album" class="text-lg text-gray-500 dark:text-gray-400">
                专辑：{{ music.album }}
              </p>
            </div>

            <!-- 统计信息 -->
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div class="text-center">
                <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(music.playCount) }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">播放量</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(music.likeCount) }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">点赞数</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(music.shareCount) }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">分享数</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                  {{ formatDuration(music.duration) }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">时长</p>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-wrap gap-3">
              <Button
                variant="primary"
                size="lg"
                @click="playMusic"
                class="flex items-center space-x-2"
              >
                <Icon :name="isCurrentTrack && playerStore.state.isPlaying ? 'pause' : 'play'" class="w-5 h-5" />
                <span>{{ isCurrentTrack && playerStore.state.isPlaying ? '暂停' : '播放' }}</span>
              </Button>

              <Button
                variant="outline"
                size="lg"
                @click="toggleLike"
                :class="{ 'text-red-500 border-red-500': music.isLiked }"
                class="flex items-center space-x-2"
              >
                <Icon :name="music.isLiked ? 'heart-solid' : 'heart'" class="w-5 h-5" />
                <span>{{ music.isLiked ? '已喜欢' : '喜欢' }}</span>
              </Button>

              <Button
                variant="outline"
                size="lg"
                @click="addToQueue"
                class="flex items-center space-x-2"
              >
                <Icon name="plus" class="w-5 h-5" />
                <span>添加到队列</span>
              </Button>

              <Button
                variant="outline"
                size="lg"
                @click="shareMusic"
                class="flex items-center space-x-2"
              >
                <Icon name="share" class="w-5 h-5" />
                <span>分享</span>
              </Button>
            </div>

            <!-- 标签 -->
            <div v-if="music.tags && music.tags.length > 0" class="flex flex-wrap gap-2">
              <span
                v-for="tag in music.tags"
                :key="tag"
                class="inline-block bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 text-sm px-3 py-1 rounded-full"
              >
                {{ tag }}
              </span>
            </div>

            <!-- 详细信息 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500 dark:text-gray-400">分类：</span>
                <span class="text-gray-900 dark:text-white">{{ music.genre || '未分类' }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">语言：</span>
                <span class="text-gray-900 dark:text-white">{{ getLanguageName(music.language) }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">音质：</span>
                <span class="text-gray-900 dark:text-white">{{ getQualityName(music.quality) }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">格式：</span>
                <span class="text-gray-900 dark:text-white">{{ music.format.toUpperCase() }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">发布时间：</span>
                <span class="text-gray-900 dark:text-white">{{ formatDate(music.releaseDate || music.createdAt) }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">文件大小：</span>
                <span class="text-gray-900 dark:text-white">{{ formatFileSize(music.fileSize) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 评论区域 -->
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          评论 ({{ music.commentCount }})
        </h2>

        <!-- 评论输入框 -->
        <div class="mb-6">
          <div class="flex space-x-3">
            <div class="w-10 h-10 bg-gray-200 dark:bg-slate-700 rounded-full flex-shrink-0"></div>
            <div class="flex-1">
              <textarea
                v-model="newComment"
                placeholder="写下你的评论..."
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              ></textarea>
              <div class="flex justify-end mt-2">
                <Button
                  variant="primary"
                  size="sm"
                  :disabled="!newComment.trim()"
                  @click="submitComment"
                >
                  发表评论
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 评论列表 -->
        <div class="space-y-4">
          <div
            v-for="comment in comments"
            :key="comment.id"
            class="flex space-x-3"
          >
            <div class="w-10 h-10 bg-gray-200 dark:bg-slate-700 rounded-full flex-shrink-0"></div>
            <div class="flex-1">
              <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-3">
                <div class="flex items-center justify-between mb-1">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ comment.user.username }}
                  </h4>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formatDate(comment.createdAt) }}
                  </span>
                </div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  {{ comment.content }}
                </p>
              </div>
              <div class="flex items-center space-x-4 mt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  :class="{ 'text-red-500': comment.isLiked }"
                  @click="toggleCommentLike(comment)"
                  class="flex items-center space-x-1"
                >
                  <Icon :name="comment.isLiked ? 'heart-solid' : 'heart'" class="w-3 h-3" />
                  <span>{{ comment.likeCount }}</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  class="text-xs"
                >
                  回复
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多评论 -->
        <div v-if="hasMoreComments" class="text-center mt-6">
          <Button
            variant="outline"
            @click="loadMoreComments"
            :loading="loadingComments"
          >
            加载更多评论
          </Button>
        </div>
      </div>

      <!-- 相关推荐 -->
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          相关推荐
        </h2>
        
        <MusicList
          :music-list="relatedMusic"
          :loading="loadingRelated"
          :show-header="false"
          :show-pagination="false"
          :grid-columns="4"
          empty-title="暂无相关推荐"
          empty-description="系统正在为你寻找相关的音乐"
          @music-click="onRelatedMusicClick"
          @music-play="onRelatedMusicPlay"
        />
      </div>
    </div>

    <!-- 404 状态 -->
    <div v-else class="flex flex-col items-center justify-center min-h-screen">
      <Icon name="musical-note" class="w-24 h-24 text-gray-300 dark:text-gray-600 mb-4" />
      <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
        音乐不存在
      </h1>
      <p class="text-gray-500 dark:text-gray-400 mb-6">
        抱歉，你访问的音乐不存在或已被删除
      </p>
      <Button variant="primary" @click="$router.push('/music')">
        返回音乐库
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music, Comment } from '~/types'

// 路由参数
const route = useRoute()
const musicId = route.params.id as string

// 页面元数据
definePageMeta({
  title: '音乐详情 - MusicDou'
})

const playerStore = usePlayerStore()
const audioPlayer = useAudioPlayer()

// 响应式数据
const loading = ref(true)
const music = ref<Music | null>(null)
const comments = ref<Comment[]>([])
const relatedMusic = ref<Music[]>([])
const newComment = ref('')
const loadingComments = ref(false)
const loadingRelated = ref(false)
const hasMoreComments = ref(true)

// 计算属性
const isCurrentTrack = computed(() => {
  return playerStore.state.currentTrack?.id === music.value?.id
})

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getLanguageName = (lang?: string): string => {
  const langMap: Record<string, string> = {
    'zh': '中文',
    'en': '英文',
    'ja': '日文',
    'ko': '韩文'
  }
  return langMap[lang || ''] || '未知'
}

const getQualityName = (quality: string): string => {
  const qualityMap: Record<string, string> = {
    'low': '标准',
    'medium': '高品质',
    'high': '超高品质',
    'lossless': '无损'
  }
  return qualityMap[quality] || '未知'
}

const loadMusicDetail = async () => {
  loading.value = true
  
  try {
    // 这里应该调用实际的API
    // const response = await $fetch(`/api/music/${musicId}`)
    
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    music.value = {
      id: musicId,
      title: '示例歌曲',
      artist: '示例艺术家',
      album: '示例专辑',
      duration: 240,
      url: 'https://example.com/music.mp3',
      coverUrl: 'https://picsum.photos/400/400?random=1',
      genre: '流行',
      tags: ['热门', '推荐', '新歌'],
      language: 'zh',
      quality: 'high',
      fileSize: 8000000,
      format: 'mp3',
      releaseDate: '2024-01-01',
      playCount: 123456,
      likeCount: 12345,
      shareCount: 1234,
      commentCount: 123,
      isLiked: false,
      isPublic: true,
      status: 'active',
      uploaderId: 'user-1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    // 加载评论和相关推荐
    loadComments()
    loadRelatedMusic()
    
  } catch (error) {
    console.error('加载音乐详情失败:', error)
    music.value = null
  } finally {
    loading.value = false
  }
}

const loadComments = async () => {
  // 模拟评论数据
  comments.value = Array.from({ length: 5 }, (_, index) => ({
    id: `comment-${index + 1}`,
    content: `这是第 ${index + 1} 条评论，非常好听的歌曲！`,
    userId: `user-${index + 1}`,
    user: {
      id: `user-${index + 1}`,
      username: `用户${index + 1}`,
      email: `user${index + 1}@example.com`,
      userGroup: 'normal' as const,
      points: 0,
      followersCount: 0,
      followingCount: 0,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    targetId: musicId,
    targetType: 'music' as const,
    likeCount: Math.floor(Math.random() * 50),
    isLiked: Math.random() > 0.5,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  }))
}

const loadRelatedMusic = async () => {
  loadingRelated.value = true
  
  try {
    // 模拟相关音乐数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    relatedMusic.value = Array.from({ length: 8 }, (_, index) => ({
      id: `related-${index + 1}`,
      title: `相关歌曲 ${index + 1}`,
      artist: `艺术家 ${index + 1}`,
      duration: 180 + Math.random() * 120,
      url: `https://example.com/related/${index + 1}.mp3`,
      coverUrl: `https://picsum.photos/300/300?random=${index + 10}`,
      genre: '流行',
      quality: 'high' as const,
      fileSize: 5000000,
      format: 'mp3',
      playCount: Math.floor(Math.random() * 50000),
      likeCount: Math.floor(Math.random() * 5000),
      shareCount: Math.floor(Math.random() * 500),
      commentCount: Math.floor(Math.random() * 100),
      isLiked: false,
      isPublic: true,
      status: 'active' as const,
      uploaderId: 'user-1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))
  } finally {
    loadingRelated.value = false
  }
}

// 事件处理
const playMusic = () => {
  if (music.value) {
    if (isCurrentTrack.value) {
      audioPlayer.togglePlay()
    } else {
      audioPlayer.playTrack(music.value)
    }
  }
}

const toggleLike = () => {
  if (music.value) {
    music.value.isLiked = !music.value.isLiked
    if (music.value.isLiked) {
      music.value.likeCount++
    } else {
      music.value.likeCount--
    }
  }
}

const addToQueue = () => {
  if (music.value) {
    playerStore.addToQueue(music.value)
  }
}

const shareMusic = () => {
  if (music.value) {
    // 实现分享功能
    console.log('分享音乐:', music.value.title)
  }
}

const submitComment = () => {
  if (newComment.value.trim()) {
    // 提交评论
    console.log('提交评论:', newComment.value)
    newComment.value = ''
  }
}

const toggleCommentLike = (comment: Comment) => {
  comment.isLiked = !comment.isLiked
  if (comment.isLiked) {
    comment.likeCount++
  } else {
    comment.likeCount--
  }
}

const loadMoreComments = () => {
  loadingComments.value = true
  // 加载更多评论
  setTimeout(() => {
    loadingComments.value = false
    hasMoreComments.value = false
  }, 1000)
}

const onRelatedMusicClick = (relatedMusic: Music) => {
  navigateTo(`/music/${relatedMusic.id}`)
}

const onRelatedMusicPlay = (relatedMusic: Music) => {
  audioPlayer.playTrack(relatedMusic)
}

// 生命周期
onMounted(() => {
  loadMusicDetail()
})

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId && newId !== musicId) {
    loadMusicDetail()
  }
})
</script>
