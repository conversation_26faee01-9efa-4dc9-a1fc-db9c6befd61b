<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面头部 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          音乐库
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          发现和播放你喜欢的音乐
        </p>
      </div>

      <!-- 搜索和筛选 -->
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <Input
              v-model="searchQuery"
              type="search"
              placeholder="搜索歌曲、艺术家或专辑..."
              :icon="'magnifying-glass'"
              @input="onSearchInput"
            />
          </div>

          <!-- 分类筛选 -->
          <div>
            <select
              v-model="selectedGenre"
              class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              @change="onFilterChange"
            >
              <option value="">所有分类</option>
              <option v-for="genre in genres" :key="genre" :value="genre">
                {{ genre }}
              </option>
            </select>
          </div>

          <!-- 排序方式 -->
          <div>
            <select
              v-model="sortBy"
              class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              @change="onSortChange"
            >
              <option value="createdAt">最新发布</option>
              <option value="playCount">播放量</option>
              <option value="likeCount">点赞数</option>
              <option value="title">歌曲名称</option>
              <option value="artist">艺术家</option>
            </select>
          </div>
        </div>

        <!-- 快速筛选标签 -->
        <div class="flex flex-wrap gap-2 mt-4">
          <Button
            v-for="tag in quickFilters"
            :key="tag.value"
            :variant="activeQuickFilter === tag.value ? 'primary' : 'ghost'"
            size="sm"
            @click="setQuickFilter(tag.value)"
          >
            {{ tag.label }}
          </Button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
              <Icon name="musical-note" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总歌曲数</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {{ formatNumber(stats.totalSongs) }}
              </p>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <Icon name="play" class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总播放量</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {{ formatNumber(stats.totalPlays) }}
              </p>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <Icon name="heart" class="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总点赞数</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {{ formatNumber(stats.totalLikes) }}
              </p>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Icon name="users" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">艺术家数</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {{ formatNumber(stats.totalArtists) }}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <!-- 音乐列表 -->
      <MusicList
        :music-list="musicList"
        :loading="loading"
        :show-header="false"
        :show-pagination="true"
        :current-page="currentPage"
        :total-pages="totalPages"
        :empty-title="emptyTitle"
        :empty-description="emptyDescription"
        @music-click="onMusicClick"
        @music-play="onMusicPlay"
        @music-like="onMusicLike"
        @add-to-queue="onAddToQueue"
        @add-to-playlist="onAddToPlaylist"
        @music-share="onMusicShare"
        @page-change="onPageChange"
        @play-all="onPlayAll"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

// 页面元数据
definePageMeta({
  title: '音乐库 - MusicDou',
  description: '发现和播放你喜欢的音乐'
})

// 响应式数据
const loading = ref(false)
const musicList = ref<Music[]>([])
const searchQuery = ref('')
const selectedGenre = ref('')
const sortBy = ref('createdAt')
const activeQuickFilter = ref('')
const currentPage = ref(1)
const totalPages = ref(1)

// 统计数据
const stats = ref({
  totalSongs: 0,
  totalPlays: 0,
  totalLikes: 0,
  totalArtists: 0
})

// 筛选选项
const genres = ref([
  '流行', '摇滚', '电子', '古典', '爵士', '民谣', '说唱', '乡村', '蓝调', '雷鬼'
])

const quickFilters = [
  { label: '全部', value: '' },
  { label: '最新', value: 'latest' },
  { label: '热门', value: 'popular' },
  { label: '推荐', value: 'recommended' },
  { label: '我的喜欢', value: 'liked' }
]

// 计算属性
const emptyTitle = computed(() => {
  if (searchQuery.value) return '未找到相关音乐'
  if (selectedGenre.value) return `暂无${selectedGenre.value}音乐`
  return '暂无音乐'
})

const emptyDescription = computed(() => {
  if (searchQuery.value) return `没有找到包含"${searchQuery.value}"的音乐，试试其他关键词吧`
  return '这里还没有任何音乐，快去发现一些好听的歌曲吧！'
})

// 防抖搜索
const debouncedSearch = useDebounceFn(() => {
  loadMusicList()
}, 500)

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const loadMusicList = async () => {
  loading.value = true
  
  try {
    // 这里应该调用实际的API
    // const response = await $fetch('/api/music', {
    //   query: {
    //     search: searchQuery.value,
    //     genre: selectedGenre.value,
    //     sortBy: sortBy.value,
    //     page: currentPage.value,
    //     limit: 20
    //   }
    // })
    
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData = generateMockMusicList()
    musicList.value = mockData.data
    totalPages.value = mockData.totalPages
    stats.value = mockData.stats
    
  } catch (error) {
    console.error('加载音乐列表失败:', error)
    // 显示错误提示
  } finally {
    loading.value = false
  }
}

const generateMockMusicList = () => {
  // 生成模拟音乐数据
  const mockMusic: Music[] = Array.from({ length: 20 }, (_, index) => ({
    id: `music-${index + 1}`,
    title: `歌曲 ${index + 1}`,
    artist: `艺术家 ${Math.floor(index / 3) + 1}`,
    album: `专辑 ${Math.floor(index / 5) + 1}`,
    duration: 180 + Math.random() * 120,
    url: `https://example.com/music/${index + 1}.mp3`,
    coverUrl: `https://picsum.photos/300/300?random=${index + 1}`,
    genre: genres.value[Math.floor(Math.random() * genres.value.length)],
    tags: ['流行', '热门'].slice(0, Math.floor(Math.random() * 2) + 1),
    language: 'zh',
    quality: 'high' as const,
    fileSize: 5000000,
    format: 'mp3',
    releaseDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    playCount: Math.floor(Math.random() * 100000),
    likeCount: Math.floor(Math.random() * 10000),
    shareCount: Math.floor(Math.random() * 1000),
    commentCount: Math.floor(Math.random() * 500),
    isLiked: Math.random() > 0.7,
    isPublic: true,
    status: 'active' as const,
    uploaderId: 'user-1',
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  }))

  return {
    data: mockMusic,
    totalPages: 5,
    stats: {
      totalSongs: 1234,
      totalPlays: 5678900,
      totalLikes: 234567,
      totalArtists: 456
    }
  }
}

// 事件处理
const onSearchInput = () => {
  currentPage.value = 1
  debouncedSearch()
}

const onFilterChange = () => {
  currentPage.value = 1
  loadMusicList()
}

const onSortChange = () => {
  currentPage.value = 1
  loadMusicList()
}

const setQuickFilter = (filter: string) => {
  activeQuickFilter.value = filter
  currentPage.value = 1
  
  // 根据快速筛选设置其他筛选条件
  switch (filter) {
    case 'latest':
      sortBy.value = 'createdAt'
      break
    case 'popular':
      sortBy.value = 'playCount'
      break
    case 'recommended':
      // 这里可以设置推荐算法的筛选条件
      break
    case 'liked':
      // 这里可以筛选用户喜欢的音乐
      break
  }
  
  loadMusicList()
}

const onMusicClick = (music: Music) => {
  // 跳转到音乐详情页
  navigateTo(`/music/${music.id}`)
}

const onMusicPlay = (music: Music) => {
  console.log('播放音乐:', music.title)
}

const onMusicLike = (music: Music) => {
  // 切换喜欢状态
  const index = musicList.value.findIndex(m => m.id === music.id)
  if (index !== -1) {
    musicList.value[index].isLiked = !musicList.value[index].isLiked
    if (musicList.value[index].isLiked) {
      musicList.value[index].likeCount++
    } else {
      musicList.value[index].likeCount--
    }
  }
}

const onAddToQueue = (music: Music) => {
  console.log('添加到队列:', music.title)
}

const onAddToPlaylist = (music: Music) => {
  console.log('添加到歌单:', music.title)
}

const onMusicShare = (music: Music) => {
  console.log('分享音乐:', music.title)
}

const onPageChange = (page: number) => {
  currentPage.value = page
  loadMusicList()
}

const onPlayAll = () => {
  console.log('播放全部音乐')
}

// 生命周期
onMounted(() => {
  loadMusicList()
})
</script>
