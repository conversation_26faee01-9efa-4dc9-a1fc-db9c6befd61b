<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          音乐播放器演示
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          测试音乐播放器的各项功能
        </p>
      </div>

      <!-- 播放器控制面板 -->
      <Card class="p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          播放器控制
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 当前播放信息 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
              当前播放
            </h3>
            
            <div v-if="playerStore.state.currentTrack" class="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
              <div class="flex items-center space-x-3">
                <div class="w-16 h-16 bg-gray-200 dark:bg-slate-600 rounded-lg overflow-hidden">
                  <img 
                    v-if="playerStore.state.currentTrack.coverUrl" 
                    :src="playerStore.state.currentTrack.coverUrl" 
                    :alt="playerStore.state.currentTrack.title"
                    class="w-full h-full object-cover"
                  >
                  <div 
                    v-else 
                    class="w-full h-full flex items-center justify-center"
                  >
                    <Icon name="musical-note" class="w-8 h-8 text-gray-400" />
                  </div>
                </div>
                
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900 dark:text-white">
                    {{ playerStore.state.currentTrack.title }}
                  </h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ playerStore.state.currentTrack.artist }}
                  </p>
                  <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                    <span>{{ formatTime(playerStore.state.currentTime) }} / {{ formatTime(playerStore.state.duration) }}</span>
                    <span>音量: {{ Math.round(playerStore.state.volume * 100) }}%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else class="bg-gray-50 dark:bg-slate-700 rounded-lg p-4 text-center">
              <Icon name="musical-note" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p class="text-gray-500 dark:text-gray-400">暂无播放内容</p>
            </div>
          </div>

          <!-- 播放器状态 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
              播放器状态
            </h3>
            
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">播放状态:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ playerStore.state.isPlaying ? '播放中' : '已暂停' }}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">随机播放:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ playerStore.state.shuffle ? '开启' : '关闭' }}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">循环模式:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ getRepeatModeText(playerStore.state.repeat) }}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">队列长度:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ playerStore.state.queue.length }} 首
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-300">当前索引:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ playerStore.state.currentIndex + 1 }} / {{ playerStore.state.queue.length }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <!-- 测试歌曲列表 -->
      <Card class="p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          测试歌曲
        </h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="(song, index) in testSongs"
            :key="song.id"
            :class="[
              'bg-gray-50 dark:bg-slate-700 rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-100 dark:hover:bg-slate-600',
              { 'ring-2 ring-primary-500': playerStore.state.currentTrack?.id === song.id }
            ]"
            @click="playTestSong(song)"
          >
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gray-200 dark:bg-slate-600 rounded-lg flex items-center justify-center">
                <Icon name="musical-note" class="w-6 h-6 text-gray-400" />
              </div>
              
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-900 dark:text-white truncate">
                  {{ song.title }}
                </h4>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ song.artist }}
                </p>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                @click.stop="addToQueue(song)"
                class="opacity-0 group-hover:opacity-100"
              >
                <Icon name="plus" class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
        
        <div class="mt-6 flex justify-center">
          <Button
            variant="primary"
            @click="playAllTestSongs"
            class="flex items-center space-x-2"
          >
            <Icon name="play" class="w-4 h-4" />
            <span>播放全部测试歌曲</span>
          </Button>
        </div>
      </Card>

      <!-- 播放队列 -->
      <Card class="p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          播放队列
        </h2>
        
        <PlayQueue />
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

// 页面元数据
definePageMeta({
  title: '音乐播放器演示 - MusicDou'
})

const playerStore = usePlayerStore()
const audioPlayer = useAudioPlayer()

// 测试歌曲数据
const testSongs: Music[] = [
  {
    id: 'test-1',
    title: '测试歌曲 1',
    artist: '测试艺术家 A',
    album: '测试专辑 1',
    duration: 210,
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // 示例音频URL
    coverUrl: 'https://picsum.photos/300/300?random=1',
    genre: '流行',
    quality: 'high',
    fileSize: 5000000,
    format: 'mp3',
    playCount: 1234,
    likeCount: 123,
    shareCount: 12,
    commentCount: 5,
    isLiked: false,
    isPublic: true,
    status: 'active',
    uploaderId: 'user-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'test-2',
    title: '测试歌曲 2',
    artist: '测试艺术家 B',
    album: '测试专辑 2',
    duration: 180,
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://picsum.photos/300/300?random=2',
    genre: '摇滚',
    quality: 'high',
    fileSize: 4500000,
    format: 'mp3',
    playCount: 2345,
    likeCount: 234,
    shareCount: 23,
    commentCount: 8,
    isLiked: true,
    isPublic: true,
    status: 'active',
    uploaderId: 'user-2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'test-3',
    title: '测试歌曲 3',
    artist: '测试艺术家 C',
    album: '测试专辑 3',
    duration: 240,
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://picsum.photos/300/300?random=3',
    genre: '电子',
    quality: 'high',
    fileSize: 6000000,
    format: 'mp3',
    playCount: 3456,
    likeCount: 345,
    shareCount: 34,
    commentCount: 12,
    isLiked: false,
    isPublic: true,
    status: 'active',
    uploaderId: 'user-3',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

// 方法
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const getRepeatModeText = (mode: string): string => {
  switch (mode) {
    case 'one':
      return '单曲循环'
    case 'all':
      return '列表循环'
    default:
      return '不循环'
  }
}

const playTestSong = (song: Music) => {
  audioPlayer.playTrack(song, testSongs)
}

const addToQueue = (song: Music) => {
  playerStore.addToQueue(song)
}

const playAllTestSongs = () => {
  if (testSongs.length > 0) {
    audioPlayer.playTrack(testSongs[0], testSongs)
  }
}
</script>
