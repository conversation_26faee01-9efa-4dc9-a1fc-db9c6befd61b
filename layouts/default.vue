<template>
  <div class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200">
    <!-- Header -->
    <LayoutHeader />

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Logo and Description -->
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <Icon name="musical-note" class="w-8 h-8 text-primary-500" />
              <span class="text-xl font-bold text-gray-900 dark:text-white">
                MusicDou
              </span>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
              现代化的音乐分享平台，为音乐爱好者量身打造。发现、分享和享受无限音乐。
            </p>
            <div class="flex space-x-4">
              <a
                href="#"
                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Icon name="facebook" class="w-5 h-5" />
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Icon name="twitter" class="w-5 h-5" />
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Icon name="instagram" class="w-5 h-5" />
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
              快速链接
            </h3>
            <ul class="space-y-2">
              <li v-for="link in quickLinks" :key="link.name">
                <NuxtLink
                  :to="link.href"
                  class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  {{ link.name }}
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
              支持
            </h3>
            <ul class="space-y-2">
              <li v-for="link in supportLinks" :key="link.name">
                <NuxtLink
                  :to="link.href"
                  class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  {{ link.name }}
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom Bar -->
        <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-500 dark:text-gray-400 text-sm">
              © {{ currentYear }} MusicDou. 保留所有权利。
            </p>
            <div class="flex space-x-6 mt-4 md:mt-0">
              <NuxtLink
                to="/privacy"
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm transition-colors"
              >
                隐私政策
              </NuxtLink>
              <NuxtLink
                to="/terms"
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm transition-colors"
              >
                服务条款
              </NuxtLink>
              <NuxtLink
                to="/contact"
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm transition-colors"
              >
                联系我们
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Global Toast Container -->
    <Toast ref="globalToast" />

    <!-- Global Loading Overlay -->
    <Loading
      v-if="isGlobalLoading"
      type="spinner"
      size="lg"
      text="加载中..."
      fullscreen
    />

    <!-- Global Music Player -->
    <MusicMusicPlayer />
  </div>
</template>

<script setup lang="ts">
// 全局状态
const isGlobalLoading = ref(false)
const globalToast = ref()

// 当前年份
const currentYear = new Date().getFullYear()

// 链接数据
const quickLinks = [
  { name: '发现音乐', href: '/discover' },
  { name: '热门歌单', href: '/playlists' },
  { name: '音乐社区', href: '/social' },
  { name: '个人中心', href: '/profile' }
]

const supportLinks = [
  { name: '帮助中心', href: '/help' },
  { name: '反馈建议', href: '/feedback' },
  { name: '开发者API', href: '/api-docs' },
  { name: '关于我们', href: '/about' }
]

// 全局方法
const showGlobalToast = (type: 'success' | 'error' | 'warning' | 'info', message: string, title?: string) => {
  if (globalToast.value) {
    globalToast.value.addToast({
      type,
      message,
      title,
      duration: 3000
    })
  }
}

const setGlobalLoading = (loading: boolean) => {
  isGlobalLoading.value = loading
}

// 提供全局方法
provide('showGlobalToast', showGlobalToast)
provide('setGlobalLoading', setGlobalLoading)

// 监听路由变化，关闭加载状态
const route = useRoute()
watch(() => route.path, () => {
  isGlobalLoading.value = false
})
</script>
